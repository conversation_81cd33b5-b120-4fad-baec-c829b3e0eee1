import { AlertCircle, CheckCircle } from 'lucide-react';
import { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import loginBg from '../assets/Login.png';
import logo from '../assets/logo.png';
import { industryOptions } from '../data';
import { toast } from 'react-toastify';


const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// const Signup = () => {
//   const [formSubmitted, setFormSubmitted] = useState(false);
//   const [formData, setFormData] = useState({
//     fullName: '',
//     company: '',
//     industry: industryOptions[0],
//     email: '',
//     phone: '',
//   });

//   const [errors, setErrors] = useState({});
//   const [loading, setLoading] = useState(false);

//   const formatPhoneNumber = (value) => {
//     const digits = value.replace(/\D/g, '');
//     let formatted = '';
//     if (digits.length > 0) {
//       formatted += digits.slice(0, 3);
//     }
//     if (digits.length > 3) {
//       formatted += '-' + digits.slice(3, 6);
//     }
//     if (digits.length > 6) {
//       formatted += '-' + digits.slice(6, 10);
//     }
//     return formatted;
//   };

//   const handlePhoneChange = (e) => {
//     const rawValue = e.target.value;
//     const formattedValue = formatPhoneNumber(rawValue);
//     setFormData((prev) => ({
//       ...prev,
//       phone: formattedValue,
//     }));

//     if (errors.phone) {
//       setErrors((prev) => ({
//         ...prev,
//         phone: '',
//       }));
//     }
//   };

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }));

//     if (errors[name]) {
//       setErrors((prev) => ({
//         ...prev,
//         [name]: '',
//       }));
//     }
//   };

//   const validateForm = () => {
//     const newErrors = {};

//     if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
//     if (!formData.company.trim()) newErrors.company = 'Company is required';

//     if (!formData.email.trim()) {
//       newErrors.email = 'Email is required';
//     } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
//       newErrors.email = 'Please enter a valid email address';
//     }

//     if (formData.phone && !/^\d{3}-\d{3}-\d{4}$/.test(formData.phone)) {
//       newErrors.phone = 'Phone number must be in the format ************';
//     }

//     setErrors(newErrors);
//     return Object.keys(newErrors).length === 0;
//   };

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     if (!validateForm()) return;

//     const queryParams = new URLSearchParams(window.location.search);
//     const pkg = queryParams.get('pkg')?.toLowerCase();
//     const dur = queryParams.get('dur')?.toLowerCase();
//     const purchase = queryParams.get('purchase')?.toLowerCase();

//     const priceMap = {
//       yearly: {
//         standard: import.meta.env.VITE_STRIPE_PRICE_YEARLY_STANDARD,
//         pro: import.meta.env.VITE_STRIPE_PRICE_YEARLY_PRO,
//         premium: import.meta.env.VITE_STRIPE_PRICE_YEARLY_PREMIUM,
//         free: "free"
//       },
//       monthly: {
//         standard: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_STANDARD,
//         pro: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_PRO,
//         premium: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_PREMIUM,
//         free: "free"
//       },
//     };


//     const selectedPriceId = priceMap[dur]?.[pkg];

//     if (!selectedPriceId && purchase === 'buy') {
//       setErrors({ stripe: 'Invalid subscription package or duration.' });
//       return;
//     }

//     const payload = {
//       ...formData,
//       userRole: 'user',
//       priceId: selectedPriceId,
//       pkg: pkg || undefined,
//       duration: dur || undefined,
//       purchase: purchase || undefined,
//     };

//     if (purchase === 'trial') {
//       payload.trialPeriodDays = 30;
//     }

//     setLoading(true);
//     try {
//       const response = await fetch(import.meta.env.VITE_SUBSCRIPTION_API, {
//         method: "POST",
//         headers: { "Content-Type": "application/json" },
//         body: JSON.stringify(payload),
//       });

//       const data = await response.json();

//       if (response.ok) {
//         if (data.userPlan === 'Free Plan' || data.userPlan === '30 Days Trial') {
//           sessionStorage.setItem("userPlan", data.userPlan);
//           sessionStorage.setItem("loginEmail", data.loginEmail);
//           sessionStorage.setItem("redirectUrl", data.redirectUrl);

//           window.location.href = data.redirectUrl || '/success';
//           return;
//         }

//         if (data.sessionId) {
//           const stripe = await stripePromise;
//           await stripe.redirectToCheckout({ sessionId: data.sessionId });
//           return;
//         }

//         setErrors({ stripe: 'Subscription failed. Please try again.' });
//       } else if (response.status === 503) {
//         sessionStorage.setItem(
//           "errorMessage",
//           "Signup is temporarily unavailable. Please try again later."
//         );
//         window.location.href = "/cancel"; 
//       } else {
//         setErrors({ stripe: data.message || 'Subscription request failed.' });
//       }

//     } catch (error) {
//       console.error('Error:', error);
//       setErrors({ stripe: 'Something went wrong. Please try again later.' });
//     } finally {
//       setLoading(false);
//     }
//   };

const Signup = () => {
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    company: '',
    industry: industryOptions[0],
    email: '',
    phone: '',
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const formatPhoneNumber = (value) => {
    const digits = value.replace(/\D/g, '');
    let formatted = '';
    if (digits.length > 0) {
      formatted += digits.slice(0, 3);
    }
    if (digits.length > 3) {
      formatted += '-' + digits.slice(3, 6);
    }
    if (digits.length > 6) {
      formatted += '-' + digits.slice(6, 10);
    }
    return formatted;
  };

  const handlePhoneChange = (e) => {
    const rawValue = e.target.value;
    const formattedValue = formatPhoneNumber(rawValue);
    setFormData((prev) => ({
      ...prev,
      phone: formattedValue,
    }));

    if (errors.phone) {
      setErrors((prev) => ({
        ...prev,
        phone: '',
      }));
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.company.trim()) newErrors.company = 'Company is required';

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.phone && !/^\d{3}-\d{3}-\d{4}$/.test(formData.phone)) {
      newErrors.phone = 'Phone number must be in the format ************';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    const queryParams = new URLSearchParams(window.location.search);
    const pkg = queryParams.get('pkg')?.toLowerCase();
    const dur = queryParams.get('dur')?.toLowerCase();
    const purchase = queryParams.get('purchase')?.toLowerCase();

    const priceMap = {
      yearly: {
        standard: import.meta.env.VITE_STRIPE_PRICE_YEARLY_STANDARD,
        pro: import.meta.env.VITE_STRIPE_PRICE_YEARLY_PRO,
        premium: import.meta.env.VITE_STRIPE_PRICE_YEARLY_PREMIUM,
        free: "free"
      },
      monthly: {
        standard: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_STANDARD,
        pro: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_PRO,
        premium: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_PREMIUM,
        free: "free"
      },
    };

    const selectedPriceId = priceMap[dur]?.[pkg];

    if (!selectedPriceId && purchase === 'buy') {
      toast.error('Invalid subscription package or duration.');
      setErrors({ stripe: 'Invalid subscription package or duration.' });
      return;
    }

    const payload = {
      ...formData,
      userRole: 'user',
      priceId: selectedPriceId,
      pkg: pkg || undefined,
      duration: dur || undefined,
      purchase: purchase || undefined,
    };

    if (purchase === 'trial') {
      payload.trialPeriodDays = 30;
    }

    setLoading(true);
    try {
      const response = await fetch(import.meta.env.VITE_SUBSCRIPTION_API, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.userPlan === 'Free Plan' || data.userPlan === '30 Days Trial') {
          sessionStorage.setItem("userPlan", data.userPlan);
          sessionStorage.setItem("loginEmail", data.loginEmail);
          sessionStorage.setItem("redirectUrl", data.redirectUrl);

          toast.success(data.message || 'Registration successful!');
          setTimeout(() => {
            window.location.href = data.redirectUrl || '/subscription/success';
          }, 1500);
          return;
        }

        if (data.sessionId) {
          toast.success('Redirecting to payment...');
          const stripe = await stripePromise;
          await stripe.redirectToCheckout({ sessionId: data.sessionId });
          return;
        }

        toast.error('Subscription failed. Please try again.');
        setErrors({ stripe: 'Subscription failed. Please try again.' });
      } else {
        switch(response.status) {
          case 400:
            toast.error(data.error || 'Invalid request. Please check your input.');
            break;
          case 409:
            if (data.canRetry) {
              // Show retry option for failed/pending payments
              toast.error(data.error || 'Payment incomplete for this email.');
              setErrors({
                stripe: data.message || 'Payment incomplete for this email.',
                canRetry: true,
                userStatus: data.userStatus
              });
            } else {
              toast.error(data.error || 'User with this email already exists.');
              setErrors({ stripe: data.error || 'User with this email already exists.' });
            }
            break;
          case 500:
            toast.error(data.error || 'Internal server error. Please try again later.');
            break;
          case 503:
            toast.error('Service temporarily unavailable. Please try again later.');
            sessionStorage.setItem(
              "cancelMessage",
              "Signup is temporarily unavailable. Please try again later."
            );
            // window.location.href = "/subscription/cancel";
            return;
          default:
            toast.error(data.message || 'Subscription request failed.');
        }
        sessionStorage.setItem("cancelMessage", data.error || 'Subscription request failed.');
        setErrors({ stripe: data.message || 'Subscription request failed.' });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Something went wrong. Please try again later.');
      setErrors({ stripe: 'Something went wrong. Please try again later.' });
    } finally {
      setLoading(false);
    }
  };

  const handleRetryPayment = async () => {
    if (!formData.email) {
      toast.error('Email is required for retry.');
      return;
    }

    const { pkg, dur, purchase } = getSelectedOptions();

    const priceMap = {
      yearly: {
        standard: import.meta.env.VITE_STRIPE_PRICE_YEARLY_STANDARD,
        pro: import.meta.env.VITE_STRIPE_PRICE_YEARLY_PRO,
        premium: import.meta.env.VITE_STRIPE_PRICE_YEARLY_PREMIUM,
        free: "free"
      },
      monthly: {
        standard: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_STANDARD,
        pro: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_PRO,
        premium: import.meta.env.VITE_STRIPE_PRICE_MONTHLY_PREMIUM,
        free: "free"
      },
    };

    const selectedPriceId = priceMap[dur]?.[pkg];

    if (!selectedPriceId) {
      toast.error('Invalid subscription package or duration.');
      return;
    }

    const payload = {
      email: formData.email,
      priceId: selectedPriceId,
    };

    if (purchase === 'trial') {
      payload.trialPeriodDays = 30;
    }

    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_SUBSCRIPTION_API.replace('/create-subscription', '/retry-payment')}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok && data.sessionId) {
        toast.success('Redirecting to payment...');
        const stripe = await stripePromise;
        await stripe.redirectToCheckout({ sessionId: data.sessionId });
      } else {
        toast.error(data.error || 'Retry payment failed. Please try again.');
        setErrors({ stripe: data.error || 'Retry payment failed. Please try again.' });
      }
    } catch (error) {
      console.error('Retry Error:', error);
      toast.error('Something went wrong. Please try again later.');
      setErrors({ stripe: 'Something went wrong. Please try again later.' });
    } finally {
      setLoading(false);
    }
  };

  return (

    <div className="flex flex-col md:flex-row h-screen">
      <div className="w-full md:w-1/2 overflow-y-auto hide-scrollbar">
        <div className="p-6 md:pb-0 px-20 md:px-12">
          <div className="">
            <div className="flex items-center">
              <img src={logo} alt="Logo" className="object-contain h-[50px] mt-10 mb-4" />
            </div>
          </div>

          <form onSubmit={handleSubmit} className="flex-grow">
            {formSubmitted && (
              <div className="mb-6 p-4 bg-green-100 text-green-700 rounded-md flex items-center">
                <CheckCircle className="mr-2" size={20} />
                <span>Thank you! Your information has been submitted successfully.</span>
              </div>
            )}

            <div className="mb-4">
              <label htmlFor="fullName" className="block text-gray-400 mb-1">
                Full Name *
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className={`w-full border ${errors.fullName ? 'border-red-500' : 'border-gray-300'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.fullName && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="mr-1" size={14} /> {errors.fullName}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="company" className="block text-gray-600 mb-1">
                Company *
              </label>
              <input
                type="text"
                id="company"
                name="company"
                value={formData.company}
                onChange={handleChange}
                className={`w-full border ${errors.company ? 'border-red-500' : 'border-gray-300'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.company && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="mr-1" size={14} /> {errors.company}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="email" className="block text-gray-600 mb-1">
                Email *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="mr-1" size={14} /> {errors.email}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="industry" className="block text-gray-600 mb-1">
                Industry
              </label>
              <div className="relative">
                <select
                  id="industry"
                  name="industry"
                  value={formData.industry}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md p-2 pr-8 appearance-none focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  {industryOptions.map((option, index) => (
                    <option key={index} value={index === 0 ? '' : option}>
                      {option}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="phone" className="block text-gray-600 mb-1">
                Phone number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handlePhoneChange}
                className={`w-full border ${errors.phone ? 'border-red-500' : 'border-gray-300'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-primary`}
                placeholder="************"
                maxLength={12}
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="mr-1" size={14} /> {errors.phone}
                </p>
              )}
            </div>

            <button
              type="submit"
              disabled={loading}
              className={`bg-primary cursor-pointer text-white px-6 py-2.5 rounded-md font-light transition duration-300 w-32 mb-4 ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Submitting...' : 'Submit'}
            </button>

            {/* Display stripe errors and retry option */}
            {errors.stripe && (
              <div className="mb-4">
                <p className="text-red-500 text-sm flex items-center">
                  <AlertCircle className="mr-1" size={14} /> {errors.stripe}
                </p>
                {errors.canRetry && (
                  <button
                    type="button"
                    onClick={handleRetryPayment}
                    disabled={loading}
                    className={`mt-2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md font-light transition duration-300 ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {loading ? 'Processing...' : 'Retry Payment'}
                  </button>
                )}
              </div>
            )}
          </form>
        </div>
      </div>

      <div className="w-full md:w-1/2 hidden md:block fixed right-0 top-0 h-screen">
        <div className="h-full w-full relative">
          <img
            src={loginBg}
            alt="Analytics dashboard with social media icons"
            className="object-cover h-full w-full"
          />
        </div>
      </div>

      <div className="hidden md:block w-full md:w-1/2"></div>
    </div>
  );
};

export default Signup;