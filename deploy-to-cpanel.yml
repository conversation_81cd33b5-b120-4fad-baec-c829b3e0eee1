trigger:
  - main
  - dev

pool:
  vmImage: 'ubuntu-latest'

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '22.x'
    displayName: 'Install Node.js'

  # Prepare the correct .env file based on the branch
  - script: |
      if [ "$(Build.SourceBranchName)" == "main" ]; then
        echo "Using production environment file (prod.env)."
        mv prod.env .env
      else
        echo "Using development environment file (.env)."
        # No action needed as the file is already named .env
      fi
    displayName: 'Prepare Environment File'

  - script: |
      npm install
      npm run build
    displayName: 'Build the project'

  # Set the deployment path based on the branch name
  - script: |
      if [ "$(Build.SourceBranchName)" == "main" ]; then
        echo "##vso[task.setvariable variable=deployPath]/"
      else
        echo "##vso[task.setvariable variable=deployPath]/dev/"
      fi
    displayName: 'Set Deployment Path Variable'

  # Upload the build folder via FTP to the correct directory
  - task: FtpUpload@2
    inputs:
      credentialsOption: 'inputs'
      serverUrl: 'ftp://2wayanalytics.com'
      username: '<EMAIL>'
      password: '$(ftpPassword)'
      rootDirectory: '$(System.DefaultWorkingDirectory)/build'
      filePatterns: '**'
      remoteDirectory: '$(deployPath)'
      clean: true
      preservePaths: true