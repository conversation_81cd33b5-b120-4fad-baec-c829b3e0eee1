import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

const Success = () => {
  const [isFreeUser, setIsFreeUser] = useState(false);
  const [isTrialUser, setIsTrialUser] = useState(false);
  const [email, setEmail] = useState('');
  const [userType, setUserType] = useState('');
  const [loading, setLoading] = useState(true);

  const loginUrl = import.meta.env.VITE_LOGIN_URL;
  const subscriptionApi = import.meta.env.VITE_CUSTOMER_API;
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  useEffect(() => {
    const storedUserPlan = sessionStorage.getItem("userPlan");
    const storedEmail = sessionStorage.getItem("loginEmail");

    const urlUserType = searchParams.get("userType");
    const stripeCustomerId = searchParams.get("customerId");


    if (urlUserType === "paid" && stripeCustomerId) {
      const fetchUser = async () => {
        try {
          const payload = { stripeCustomerId };
          const response = await fetch(subscriptionApi, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          });

          const data = await response.json();

          if (data?.user) {
            setEmail(data.user);
            setUserType(data.userPlan);
            setIsFreeUser(data.userPlan === "Free Plan");
            setIsTrialUser(data.userPlan === "Free Trial");
          }
        } catch (err) {
          console.error("Error fetching user info:", err);
        } finally {
          setLoading(false);
        }
      };

      fetchUser();
    } else {
      if (storedUserPlan) {
        setUserType(storedUserPlan);
        setIsFreeUser(storedUserPlan === "Free Plan");
        setIsTrialUser(storedUserPlan === "Free Trial");
      }
      if (storedEmail) {
        setEmail(storedEmail);
      }
      setLoading(false);
    }
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center text-white text-xl">
        Loading your data...
      </div>
    );
  }

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden"
      style={{
        background: "linear-gradient(135deg, rgb(16, 53, 100) 0%, rgb(1, 184, 239) 100%)",
      }}
    >
      <div className="max-w-3xl w-full bg-white rounded-2xl shadow-2xl p-12 text-center relative z-10">
        <div className="mb-8">
          <div
            className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6"
            style={{
              background: "linear-gradient(135deg, rgb(16, 53, 100) 0%, rgb(1, 184, 239) 100%)",
            }}
          >
            <svg className="w-10 h-10 stroke-white fill-none stroke-2" viewBox="0 0 24 24">
              <polyline points="20,6 9,17 4,12" />
            </svg>
          </div>
        </div>

        <h1
          className="text-5xl font-bold mb-6"
          style={{
            background: "linear-gradient(135deg, rgb(16, 53, 100) 0%, rgb(1, 184, 239) 100%)",
            WebkitBackgroundClip: "text",
            backgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Congratulations!
        </h1>

        <div className="mb-8">
          <p className="text-2xl text-gray-700 leading-relaxed mb-4">
            You have completed the first step for the configuration of your{" "}
            <span className="font-semibold" style={{ color: "rgb(16, 53, 100)" }}>
              2wayanalytics
            </span>{" "}
            application.
          </p>

          <div className="flex flex-col items-start gap-3 text-lg text-gray-600 bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-3">
              <svg
                className="w-6 h-6 fill-none stroke-2"
                style={{ stroke: "rgb(1, 184, 239)" }}
                viewBox="0 0 24 24"
              >
                <path d="m4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2h-16c-1.1 0-2-.9-2-2v-12c0-1.1.9-2 2-2z" />
                <polyline points="22,6 12,13 2,6" />
              </svg>
              <p className="font-medium">Continue your 2Way App set‑up by clicking this link:</p>
            </div>

            <a
              href={loginUrl}
              className="text-blue-600 underline break-all"
              target="_blank"
              rel="noopener noreferrer"
            >
              {loginUrl}
            </a>

            <div>
              {(isFreeUser || isTrialUser || email) && (
                <p>
                  {isFreeUser && <span>Your Free Plan is now active.<br /></span>}
                  {isTrialUser && <span>Your trial is now active.<br /></span>}
                  For authentication, please use:<br />
                  <strong>Email:</strong> {email}<br />
                  {isFreeUser && (
                    <>
                      <strong>Password:</strong> Generic Password
                    </>
                  )}
                </p>
              )}
            </div>
          </div>

          <button
            onClick={() => (window.location.href = loginUrl)}
            className="mt-4 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white text-lg font-semibold rounded-xl transition"
          >
            Go to Login
          </button>
        </div>

        <div className="flex justify-center gap-2 mb-6">
          <div className="w-3 h-3 rounded-full animate-pulse bg-[#103564]" />
          <div
            className="w-3 h-3 rounded-full animate-pulse bg-[#01b8ef]"
            style={{ animationDelay: "0.2s" }}
          />
          <div
            className="w-3 h-3 rounded-full animate-pulse bg-[#103564]"
            style={{ animationDelay: "0.4s" }}
          />
        </div>

        <p className="text-gray-500 text-sm">Thank you for choosing 2wayanalytics</p>
      </div>

      <div className="absolute top-10 left-10 w-32 h-32 bg-white opacity-10 rounded-full blur-2xl" />
      <div className="absolute bottom-10 right-10 w-48 h-48 bg-white opacity-5 rounded-full blur-3xl" />
    </div>
  );
};

export default Success;
