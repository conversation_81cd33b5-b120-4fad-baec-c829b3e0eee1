import Success from "./components/Success";
import Cancel from "./components/Cancel";
import Signup from "./components/signup";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

const App = () => {
   return (
    <Router basename="subscription">
      <div className="App">
        <Routes>
          <Route path="/" element={<Signup />} />
          <Route path="/success" element={<Success />} />
          <Route path="/cancel" element={<Cancel />} />
        </Routes>
      </div>
    </Router>
  );
};

export default App;