{"name": "test-project", "private": true, "version": "0.0.0", "type": "module", "homepage": "/subscription", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@stripe/stripe-js": "^7.0.0", "@tailwindcss/vite": "^4.1.3", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-feather": "^2.0.10", "react-input-mask": "^2.0.4", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}