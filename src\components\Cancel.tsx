import { useEffect, useState } from "react";

const Cancel = () => {
  const defaultMessage =
    "It looks like you didn’t complete the setup for your 2wayanalytics application.";

  const [errorMessage, setErrorMessage] = useState(defaultMessage);
  const [isRetryVisible, setIsRetryVisible] = useState(false);

  useEffect(() => {
    const storedMessage = sessionStorage.getItem("cancelMessage");
    const showRetry = sessionStorage.getItem("showRetry");

    if (storedMessage) setErrorMessage(storedMessage);
    if (showRetry === "true") setIsRetryVisible(true);

    // Optional: clear values after reading
    sessionStorage.removeItem("cancelMessage");
    sessionStorage.removeItem("showRetry");
  }, []);

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden"
      style={{
        background: "linear-gradient(135deg, rgb(100, 16, 16) 0%, rgb(239, 1, 1) 100%)",
      }}
    >
      <div className="max-w-3xl w-full bg-white rounded-2xl shadow-2xl p-12 text-center relative z-10">
        {/* Icon */}
        <div className="mb-8">
          <div
            className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6"
            style={{
              background: "linear-gradient(135deg, rgb(100, 16, 16) 0%, rgb(239, 1, 1) 100%)",
            }}
          >
            <svg className="w-10 h-10 stroke-white fill-none stroke-2" viewBox="0 0 24 24">
              <line x1="6" y1="6" x2="18" y2="18" />
              <line x1="6" y1="18" x2="18" y2="6" />
            </svg>
          </div>
        </div>

        {/* Title */}
        <h1
          className="text-5xl font-bold mb-6"
          style={{
            background: "linear-gradient(135deg, rgb(100, 16, 16) 0%, rgb(239, 1, 1) 100%)",
            WebkitBackgroundClip: "text",
            backgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Subscription Cancelled
        </h1>

        {/* Message */}
        <div className="mb-8">
          <p className="text-2xl text-gray-700 leading-relaxed mb-4">{errorMessage}</p>

          <div className="flex items-center justify-center gap-3 text-lg text-gray-600 bg-gray-50 rounded-lg p-4">
            <svg
              className="w-6 h-6 fill-none stroke-2"
              style={{ stroke: "rgb(239, 1, 1)" }}
              viewBox="0 0 24 24"
            >
              <circle cx="12" cy="12" r="10" />
              <line x1="15" y1="9" x2="9" y2="15" />
              <line x1="9" y1="9" x2="15" y2="15" />
            </svg>
            <p>{ errorMessage ||`If this was a mistake, you can try the setup again anytime.`}</p>
          </div>
        </div>

        {/* Retry Button */}
        {isRetryVisible && (
          <button
            onClick={() => (window.location.href = "/signup")}
            className="mt-6 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
          >
            Try Again
          </button>
        )}

        {/* Pulsing Dots */}
        <div className="flex justify-center gap-2 my-6">
          <div className="w-3 h-3 rounded-full animate-pulse" style={{ background: "rgb(100, 16, 16)" }}></div>
          <div
            className="w-3 h-3 rounded-full animate-pulse"
            style={{ background: "rgb(239, 1, 1)", animationDelay: "0.2s" }}
          ></div>
          <div
            className="w-3 h-3 rounded-full animate-pulse"
            style={{ background: "rgb(100, 16, 16)", animationDelay: "0.4s" }}
          ></div>
        </div>

        {/* Footer */}
        <p className="text-gray-500 text-sm">
          You can restart the process whenever you're ready.
        </p>
      </div>

      {/* Background Effects */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-white opacity-10 rounded-full blur-2xl"></div>
      <div className="absolute bottom-10 right-10 w-48 h-48 bg-white opacity-5 rounded-full blur-3xl"></div>
    </div>
  );
};

export default Cancel;
